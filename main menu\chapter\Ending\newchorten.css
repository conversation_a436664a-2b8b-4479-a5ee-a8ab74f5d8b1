/* Base Styles */
body {
    margin: 0;
    overflow: hidden;
    font-family: 'Press Start 2P', cursive;
    background-color: #000;
}

canvas {
    display: block;
    margin: 0 auto;
}

/* Message Boxes */
.message-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #FFD700;
    padding: 2rem;
    border-radius: 10px;
    border: 4px solid #DAA520;
    text-align: center;
    max-width: 80%;
    z-index: 100;
    box-shadow: 0 0 20px rgba(218, 165, 32, 0.7);
}

/* Message boxes */
#message {
    display: none; /* Hidden by default - shows after girl reaches chorten */
}

#congrats-message {
    display: none; /* Hidden by default */
}

.hidden {
    display: none !important;
}

/* Other message styling */
#message, #congrats-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: gold;
    padding: 20px;
    border-radius: 10px;
    border: 3px solid goldenrod;
    text-align: center;
    z-index: 100;
}

/* Buttons */
.game-button {
    margin-top: 1.5rem;
    padding: 0.8rem 1.5rem;
    background-color: #DAA520;
    color: #000;
    border: none;
    border-radius: 5px;
    font-family: 'Press Start 2P', cursive;
    font-size: 0.7rem;
    cursor: pointer;
    transition: all 0.2s;
}

.game-button:hover {
    background-color: #FFD700;
    transform: scale(1.05);
}

/* Glitter Particles */
#glitter {
    position: fixed;
    top: 0; left: 0; width: 100vw; height: 100vh;
    pointer-events: none;
    z-index: 9999;
    background: transparent;
}

.glitter-dot {
    position: absolute;
    width: 8px; height: 8px;
    border-radius: 50%;
    background: radial-gradient(circle, #fff, #ffd700, transparent 70%);
    opacity: 0.8;
    animation: glitter-pop 1s linear forwards;
}

@keyframes glitter-pop {
    0% { transform: scale(0.5); opacity: 1; }
    80% { transform: scale(1.5); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
}

.glitter-particle {
    position: absolute;
    width: 8px;
    height: 8px;
    background-color: gold;
    border-radius: 50%;
    pointer-events: none;
    z-index: 10;
    opacity: 0;
    animation: glitter-fall 2s forwards;
}

@keyframes glitter-fall {
    0% {
        transform: translate(0, 0) scale(0.5);
        opacity: 0;
    }
    20% {
        opacity: 1;
    }
    100% {
        transform: translate(var(--tx), var(--ty)) scale(1);
        opacity: 0;
    }
}
